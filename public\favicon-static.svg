<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="32" height="32">
  <defs>
    <!-- Gradient for the globe -->
    <radialGradient id="globeGradient" cx="0.3" cy="0.3" r="0.8">
      <stop offset="0%" style="stop-color:#4F46E5;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#2563EB;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </radialGradient>
    
    <!-- Gradient for continents -->
    <linearGradient id="continentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10B981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>
    
    <!-- Shadow filter -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="1" dy="1" stdDeviation="1" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background circle (globe) -->
  <circle cx="16" cy="16" r="14" fill="url(#globeGradient)" filter="url(#shadow)" />
  
  <!-- Continents/landmasses -->
  <g fill="url(#continentGradient)" opacity="0.8">
    <!-- North America -->
    <path d="M8 10 Q12 8 15 10 Q18 12 16 15 Q14 18 10 16 Q6 14 8 10 Z" />
    
    <!-- Europe/Africa -->
    <path d="M18 8 Q22 6 25 9 Q28 12 26 16 Q24 20 20 18 Q16 16 18 12 Q18 10 18 8 Z" />
    
    <!-- Asia -->
    <path d="M20 20 Q24 18 28 21 Q30 24 28 27 Q25 29 22 27 Q19 25 20 22 Q20 21 20 20 Z" />
    
    <!-- South America -->
    <path d="M10 20 Q13 18 15 22 Q16 26 13 28 Q10 29 8 26 Q6 23 8 21 Q9 20 10 20 Z" />
  </g>
  
  <!-- Highlight/shine effect -->
  <ellipse cx="12" cy="12" rx="4" ry="6" fill="rgba(255,255,255,0.2)" opacity="0.6" />
  
  <!-- Orbital ring (representing global connection) -->
  <circle cx="16" cy="16" r="15" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="0.5" opacity="0.7" />
</svg>
