import React, { useState, useEffect, useRef, useCallback } from 'react';
import { fetchSocialMediaContent, truncateText } from '../../utils/socialMediaUtils';

interface MediaEmbedProps {
  url: string;
  platform: 'instagram' | 'facebook';
  caption?: string;
  showCaption?: boolean;
  maxCaptionLength?: number;
  fetchCaption?: boolean; // Whether to fetch caption from social media API
  onLoad?: () => void;
  onError?: (error: Error) => void;
}

const MediaEmbed: React.FC<MediaEmbedProps> = ({
  url,
  platform,
  caption,
  showCaption = true,
  maxCaptionLength = 150,
  fetchCaption = false,
  onLoad,
  onError
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [captionExpanded, setCaptionExpanded] = useState(false);
  const [dynamicCaption, setDynamicCaption] = useState<string>('');
  const [captionLoading, setCaptionLoading] = useState(false);
  const embedRef = useRef<HTMLDivElement>(null);

  const getUrlId = useCallback((url: string): string => {
    // Extract ID from URL
    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname;
      const parts = pathname.split('/').filter(part => part.length > 0);

      // For Instagram URLs like https://www.instagram.com/dast.foundation/reel/DMVSUg0NCQk/
      // the ID is the last part
      if (url.includes('instagram.com')) {
        return parts[parts.length - 1] || 'unknown';
      }

      // For Facebook URLs, return 'unknown' as we'll use the full URL for embedding
      return 'unknown';
    } catch (error) {
      console.error('Error parsing URL:', error);
      return 'unknown';
    }
  }, []);

  const handleError = useCallback((errorMsg: string) => {
    setError(errorMsg);
    setLoading(false);
    if (onError) {
      onError(new Error(errorMsg));
    }
  }, [onError]);

  const loadEmbed = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Simulate loading delay
      await new Promise(resolve => setTimeout(resolve, 800));

      if (!embedRef.current) return;

      // Clear previous content
      embedRef.current.innerHTML = '';

      // Create embed container
      const embedContainer = document.createElement('div');
      embedContainer.className = 'w-full h-full';

      if (platform === 'instagram') {
        // For Instagram, we would normally use the oEmbed API
        // For this implementation, we'll simulate with an iframe
        const iframe = document.createElement('iframe');
        // Add parameters to hide captions and text
        iframe.src = `https://www.instagram.com/p/${getUrlId(url)}/embed?cr=1&v=14&wp=1080&rd=https%3A%2F%2Fwww.instagram.com&rp=%2Fp%2F${getUrlId(url)}%2F#%7B%22ci%22%3A0%2C%22os%22%3A0%7D`;
        iframe.width = '100%';
        iframe.height = '100%';
        iframe.style.border = 'none';
        iframe.style.overflow = 'hidden';
        iframe.allow = 'autoplay; encrypted-media';
        iframe.onload = () => {
          setLoading(false);
          if (onLoad) {
            onLoad();
          }
        };
        iframe.onerror = () => {
          handleError('Failed to load Instagram embed');
        };
        embedContainer.appendChild(iframe);
      } else if (platform === 'facebook') {
        // For Facebook, create the embed URL with captions disabled
        const embedUrl = `https://www.facebook.com/plugins/video.php?href=${encodeURIComponent(url)}&show_text=0&width=560&height=315`;
        const iframe = document.createElement('iframe');
        iframe.src = embedUrl;
        iframe.width = '100%';
        iframe.height = '100%';
        iframe.style.border = 'none';
        iframe.allow = 'autoplay; clipboard-write; encrypted-media; picture-in-picture; web-share';
        iframe.allowFullscreen = true;
        iframe.onload = () => {
          setLoading(false);
          if (onLoad) {
            onLoad();
          }
        };
        iframe.onerror = () => {
          handleError('Failed to load Facebook embed');
        };
        embedContainer.appendChild(iframe);
      }

      embedRef.current.appendChild(embedContainer);
    } catch (error) {
      console.error('Error loading media embed:', error);
      handleError('Failed to load media embed');
    }
  }, [url, platform, onLoad, getUrlId, handleError]);

  // Fetch caption if requested and not provided
  useEffect(() => {
    const fetchCaptionData = async () => {
      if (fetchCaption && !caption) {
        setCaptionLoading(true);
        try {
          const content = await fetchSocialMediaContent(url, platform);
          if (content?.caption) {
            setDynamicCaption(content.caption);
          }
        } catch (error) {
          console.error('Error fetching caption:', error);
        } finally {
          setCaptionLoading(false);
        }
      }
    };

    fetchCaptionData();
  }, [url, platform, caption, fetchCaption]);

  useEffect(() => {
    loadEmbed();
  }, [url, platform, loadEmbed]);


  const handleRetry = () => {
    loadEmbed();
  };

  const toggleCaption = () => {
    setCaptionExpanded(!captionExpanded);
  };

  // Use provided caption or dynamically fetched caption
  const displayCaption = caption || dynamicCaption;
  const shouldShowExpandButton = displayCaption && displayCaption.length > maxCaptionLength;

  return (
    <div className="relative w-full h-full flex flex-col">
      {/* Media Embed Container */}
      <div className="relative flex-1">
        {loading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-200">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        )}

        {error && (
          <div className="absolute inset-0 flex flex-col items-center justify-center bg-gray-100 p-4 text-center">
            <p className="text-red-500 mb-4">{error}</p>
            <button
              onClick={handleRetry}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition"
            >
              Retry
            </button>
          </div>
        )}

        <div
          ref={embedRef}
          className={`w-full h-full ${loading || error ? 'invisible' : 'visible'}`}
        />
      </div>

      {/* Caption Section */}
      {showCaption && displayCaption && (
        <div className="bg-white border-t border-gray-200 p-4">
          {captionLoading ? (
            <div className="flex items-center text-gray-500">
              <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-gray-400 mr-2"></div>
              <span className="text-sm">Loading caption...</span>
            </div>
          ) : (
            <p className="text-gray-700 text-sm leading-relaxed">
              {captionExpanded ? displayCaption : truncateText(displayCaption, maxCaptionLength)}
              {shouldShowExpandButton && (
                <button
                  onClick={toggleCaption}
                  className="ml-2 text-blue-500 hover:text-blue-700 font-medium text-sm"
                >
                  {captionExpanded ? 'Show less' : 'Show more'}
                </button>
              )}
            </p>
          )}
        </div>
      )}
    </div>
  );
};

export default MediaEmbed;