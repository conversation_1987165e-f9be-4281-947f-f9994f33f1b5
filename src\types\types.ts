export interface TabContent {
  id: string;
  title: string;
  content: string;
}

export interface ProgramCard {
  id: string;
  title: string;
  description: string;
  icon: string;
  size?: 'small' | 'medium' | 'large';
  backgroundImage?: string; // Added optional backgroundImage property
}

export interface ResourceItem {
  id: string;
  title: string;
  description: string;
}

export interface NavItem {
  label: string;
  href: string;
}
