import React, { useState } from 'react';
import { useMediaData } from '../hooks/useMediaData';
import TrendingSection from '../components/media/TrendingSection';
import SocialMediaFeed from '../components/media/SocialMediaFeed';

const MediaPage: React.FC = () => {
  const { trendingVideo, instagramPosts, facebookPosts, loading, error } = useMediaData();
  const [retryCount, setRetryCount] = useState(0);

  const handleRetry = () => {
    setRetryCount(retryCount + 1);
  };

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center p-8 bg-white rounded-xl shadow-lg max-w-md">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Unable to Load Media Content</h2>
          <p className="text-gray-600 mb-6">There was an error loading the media content. Please try again.</p>
          <button 
            onClick={handleRetry}
            className="px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <header className="mb-12 text-center">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">Media Center</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Watch our latest videos and stay updated with our social media content
          </p>
        </header>

        {loading && retryCount === 0 ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : (
          <>
            {trendingVideo && <TrendingSection video={trendingVideo} />}
            <SocialMediaFeed 
              instagramPosts={instagramPosts} 
              facebookPosts={facebookPosts} 
              loading={loading}
            />
          </>
        )}
      </div>
    </div>
  );
};

export default MediaPage;