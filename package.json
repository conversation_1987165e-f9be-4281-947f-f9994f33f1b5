{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emailjs/browser": "^4.4.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.6.1", "three": "^0.177.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/three": "^0.176.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^6.3.5"}}