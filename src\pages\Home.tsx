import React from 'react';
import Hero from '../components/Hero';
import FocusSection from '../components/FocusSection';
import BentoGrid from '../components/BentoGrid';
import EnhancedTabsSection from '../components/EnhancedTabsSection'; // Added import
import ImpactAreas from '../components/ImpactAreas';
import ResourcesSection from '../components/ResourcesSection';
import MainGoalsSection from '../components/MainGoalsSection'; // Added import for the new section

const Home: React.FC = () => {
  return (
    <>
      <Hero />
      <FocusSection />
      <BentoGrid />
      <EnhancedTabsSection /> {/* Added new component */}
      <MainGoalsSection /> {/* Added the new section here */}
      <ImpactAreas />
      <ResourcesSection />
    </>
  );
};

export default Home;
