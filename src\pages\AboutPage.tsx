import React, { useState, useEffect } from 'react';
import { Heart, Shield, Users as UsersIcon, Globe, Target, Award } from 'lucide-react';
import HistorySection from '../components/HistorySection';

const storySliderImages = [
  '/images/image34.jpg',
  '/images/image35.jpg',
  '/images/image37.jpg',
  '/images/image38.jpg',
];

// --- Value Card ---
interface ValueCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

const ValueCard: React.FC<ValueCardProps> = ({ icon, title, description }) => (
  <div className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 h-full border border-gray-200/80">
    <div className="flex items-center mb-4">
      <div className="p-3 bg-primary-100 rounded-full mr-4">
        {icon}
      </div>
      <h3 className="text-xl font-semibold text-gray-800">{title}</h3>
    </div>
    <p className="text-gray-600 text-sm leading-relaxed">{description}</p>
  </div>
);

const valuesData = [
  { icon: <Heart size={28} className="text-primary-600" />, title: 'Compassion', description: 'We approach every individual with love, understanding, and dignity, fostering an environment of empathy and support.' },
  { icon: <Shield size={28} className="text-primary-600" />, title: 'Protection', description: 'We are steadfastly committed to safeguarding the vulnerable and creating secure, nurturing spaces for all.' },
  { icon: <UsersIcon size={28} className="text-primary-600" />, title: 'Community', description: 'We believe in the transformative power of community-driven solutions and collective, unified action.' },
  { icon: <Globe size={28} className="text-primary-600" />, title: 'Global Impact', description: 'Our vision and efforts extend beyond local borders, aiming to create meaningful and lasting worldwide change.' },
  { icon: <Target size={28} className="text-primary-600" />, title: 'Purpose-Driven', description: 'Every action we undertake is guided by our core mission to positively transform lives and uplift communities.' },
  { icon: <Award size={28} className="text-primary-600" />, title: 'Excellence', description: 'We consistently strive for the highest standards of quality and effectiveness in all our programs and services.' },
];

// --- Team Member Card ---
interface TeamMember {
  name: string;
  title: string;
  description?: string;
  imageUrl?: string;
  placeholderType?: 'male' | 'female';
}

const malePlaceholders = ['/images/man.png', '/images/man2.png', '/images/man3.png'];
const femalePlaceholder = '/images/woman.png';

const TeamMemberCard: React.FC<TeamMember> = ({ name, title, description, imageUrl, placeholderType }) => {
  let displayImageUrl = imageUrl;

  if (!displayImageUrl) {
    if (placeholderType === 'female') {
      displayImageUrl = femalePlaceholder;
    } else if (placeholderType === 'male') {
      // displayImageUrl = malePlaceholders[Math.floor(Math.random() * malePlaceholders.length)];
      displayImageUrl = malePlaceholders[0]; // Always use the first male placeholder
    } else {
      displayImageUrl = malePlaceholders[0]; 
    }
  }

  return (
    <div className="bg-white p-6 rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 h-full flex flex-col items-center transform hover:-translate-y-1.5 border border-gray-200/80 group">
      <div className="relative mb-5">
        <img 
          src={displayImageUrl} 
          alt={name} 
          className="w-32 h-32 sm:w-36 sm:h-36 rounded-full mx-auto object-cover border-4 border-gray-100 group-hover:border-primary-400 transition-all duration-300 shadow-md" 
        />
      </div>
      <h4 className="text-lg sm:text-xl font-semibold text-gray-800 text-center">{name}</h4>
      <p className="text-sm sm:text-md text-primary-600 mb-2 font-medium">{title}</p>
      {description && <p className="text-xs sm:text-sm text-gray-500 mt-1 flex-grow leading-relaxed px-2">{description}</p>}
    </div>
  );
};

const teamMembers = {
  executive: [
    { name: 'CAROLINE EKE', title: 'Executive Director', description: 'Guiding the executive functions of MADAST to achieve its strategic goals and operational excellence.', placeholderType: 'female' as const },
        { name: 'PRINCEWILL EKE', title: 'Director', description: 'Contributing to the strategic directorship and visionary planning for MADAST\'s future.', placeholderType: 'male' as const },
        { name: 'JEAN LOUIS', title: 'Secretary', description: 'Serving as Secretary, meticulously supporting the organizational mission and governance of MADAST.', placeholderType: 'male' as const },
  ],
};





const AboutPage: React.FC = () => {
  const [currentStorySlide, setCurrentStorySlide] = useState(0);

  useEffect(() => {
    const storyInterval = setInterval(() => {
      setCurrentStorySlide((prev) => (prev + 1) % storySliderImages.length);
    }, 4500); // Image change interval
    return () => clearInterval(storyInterval);
  }, []);

  return (
    <>
      {/* Hero Section - Enhanced */}
      <section className="relative text-white py-24 md:py-32 bg-cover bg-center" style={{ backgroundImage: "url('/images/a1.jpg')" }}>
        <div className="absolute inset-0 bg-black opacity-50"></div> {/* Overlay for contrast */}
        <div className="container mx-auto px-6 text-center relative z-10"> {/* Ensure content is above overlay */}
          <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold mb-6 leading-tight" style={{ textShadow: '2px 2px 4px rgba(0,0,0,0.5)' }}> {/* Adjusted shadow for better visibility */}
            About <span className="text-yellow-300 tracking-wide">MADAST</span>
          </h1>
          <p className="text-lg md:text-xl max-w-3xl mx-auto text-neutral-100 leading-relaxed" style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.2)' }}>
            Ministers Against Drug Abuse and Sex Trafficking: A dedicated faith-based organization committed to fostering safer communities through proactive prevention, comprehensive education, and compassionate support.
          </p>
        </div>
      </section>

      {/* Our Story Section - Enhanced Layout */}
      <section className="py-16 md:py-24 bg-gray-50">
        <div className="container mx-auto px-6">
          <div className="flex flex-col lg:flex-row items-stretch gap-10 lg:gap-16">
            {/* Text Content Side */}
            <div className="lg:w-1/2 flex flex-col justify-center py-6">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-8 text-center lg:text-left"> {/* Removed text-gradient-animated */}
                Our Journey & Commitment
              </h2>
              <div className="space-y-5 text-neutral-700 text-base md:text-lg leading-relaxed">
                <p>
                  Founded in 2024, Drug Abuse and Sex Trafficking Foundation (DAST Foundation) emerged from a shared commitment among faith leaders to confront the devastating impact of exploitation in our communities. Witnessing firsthand the urgent need for action, DAST Foundation was established to provide a beacon of hope and a catalyst for change.
                </p>
                <p>
                  <strong>Our nonprofit is not just for faith leaders and religious organizations; we serve the public through our faith-based initiatives.</strong> From our inception, we have been dedicated to a proactive and compassionate approach. Our core mission is to create safer, healthier communities by combating drug abuse and sex trafficking through comprehensive prevention programs, robust education, and unwavering support services. We believe deeply in the power of community-driven solutions and faith-based initiatives to foster healing, recovery, and resilience.
                </p>
                <p>
                  <strong>While we work with faith leaders, we collaborate with a wide range of partners, including government agencies, civil society organizations, schools, teachers, social workers, foundations, philanthropic organizations, the media, health workers, and other nonprofits.</strong> This collaborative approach ensures that our impact reaches every corner of society, creating a comprehensive network of support and advocacy.
                </p>
                <p>
                  As a young and dynamic organization, DAST Foundation is actively engaging with current causes and events, developing innovative strategies, and building strong networks to protect the vulnerable. Our focus is on equipping faith communities with the tools, knowledge, and support networks necessary to make a tangible difference. We are committed to growing our impact, inspired by a vision of a world free from addiction and exploitation, where every individual can thrive.
                </p>
              </div>
            </div>
            {/* Image Slider Side - Ensuring it fills the div */}
            <div className="lg:w-1/2 relative min-h-[380px] sm:min-h-[480px] md:min-h-[550px] lg:min-h-full rounded-2xl overflow-hidden shadow-2xl group">
              {storySliderImages.map((image, index) => (
                <div
                  key={image}
                  className={`absolute inset-0 w-full h-full bg-cover bg-center transition-opacity duration-1000 ease-in-out ${
                    index === currentStorySlide ? 'opacity-100' : 'opacity-0'
                  }`}
                  style={{ backgroundImage: `url(${image})` }}
                />
              ))}
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/20 group-hover:from-black/70 transition-all duration-300"></div>
            </div>
          </div>
        </div>
      </section>
        
      {/* Our Values Section - Enhanced Styling */}
      <section className="py-16 md:py-24 bg-white">
        <div className="container mx-auto px-6">
          <div className="text-center mb-14 md:mb-20">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 title-underline-white mb-3"> {/* Removed text-gradient-animated */}
              Our Guiding Values
            </h2>
            <p className="text-lg text-neutral-600 mt-2 max-w-3xl mx-auto leading-relaxed"> 
              These core principles are the bedrock of our organization, guiding every decision, action, and interaction as we strive to serve our communities with integrity and dedication.
            </p>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 md:gap-10">
            {valuesData.map((value) => (
              <ValueCard
                key={value.title}
                icon={value.icon}
                title={value.title}
                description={value.description}
              />
            ))}
          </div>
        </div>
      </section>

      {/* Meet Our Team Section - Enhanced Styling */}
      <section className="py-16 md:py-24 bg-slate-100"> {/* Light slate background */}
        <div className="container mx-auto px-6">
          <div className="text-center mb-14 md:mb-20">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 title-underline-white mb-3"> {/* Removed text-gradient-animated */}
              Meet Our Dedicated Team
            </h2>
            <p className="text-lg text-neutral-600 mt-2 max-w-3xl mx-auto leading-relaxed"> 
              A passionate and diverse group of individuals committed to our mission, bringing a wealth of experience and dedication to the communities we serve.
            </p>
          </div>

          {/* President's Profile - Enhanced Feature */}
          <div className="mb-16 md:mb-20 p-8 md:p-10 bg-gradient-to-br from-primary-500 via-purple-500 to-secondary-500 text-white rounded-2xl shadow-xl flex flex-col lg:flex-row items-center gap-8 lg:gap-12">
            <img 
              src="/images/chuck.jpg" 
              alt="Chukwudi Chuck Eke, FCIA" 
              className="w-40 h-40 md:w-52 md:h-52 rounded-full object-cover border-4 border-white shadow-lg flex-shrink-0"
            />
            <div className="text-center lg:text-left flex-1">
              <h3 className="text-2xl md:text-3xl font-bold">Chukwudi Chuck Eke, FCIA</h3>
              <p className="text-yellow-300 font-semibold text-lg md:text-xl mt-1">President</p>
              <p className="mt-4 text-neutral-100 max-w-xl text-base leading-relaxed">
                As President of MADAST, Chukwudi "Chuck" Eke brings visionary leadership and a profound dedication to combating drug abuse and sex trafficking. His commitment is pivotal in steering our organization towards impactful community engagement and systemic change.
              </p>
            </div>
          </div>

          {/* Executive Members - Enhanced Styling */}
          <div className="mb-16 md:mb-20">
            <h3 className="text-2xl md:text-3xl font-semibold text-neutral-700 mb-12 text-center relative">
              <span className="inline-block px-4 bg-slate-100 relative z-10">Executive Members</span>
              <span className="absolute left-0 right-0 top-1/2 w-full h-px bg-gray-300 -z-0"></span>
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-8 gap-y-10">
              {teamMembers.executive.map(member => (
                <TeamMemberCard key={member.name} {...member} />
              ))}
            </div>
          </div>

          </div>
        </section>

      {/* History Section */}
      <HistorySection />
    </>
  );
};

export default AboutPage;
