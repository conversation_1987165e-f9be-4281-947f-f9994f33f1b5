/**
 * Utility functions for social media content fetching and processing
 */

export interface SocialMediaContent {
  caption: string;
  likes?: number;
  comments?: number;
  timestamp?: Date;
  username?: string;
  avatar?: string;
}

/**
 * Extract post ID from Instagram URL
 */
export const extractInstagramPostId = (url: string): string | null => {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    const parts = pathname.split('/').filter(part => part.length > 0);
    
    // For Instagram URLs like https://www.instagram.com/dast.foundation/reel/DMVSUg0NCQk/
    // the ID is the last part
    if (url.includes('instagram.com')) {
      return parts[parts.length - 1] || null;
    }
    
    return null;
  } catch (error) {
    console.error('Error parsing Instagram URL:', error);
    return null;
  }
};

/**
 * Extract post ID from Facebook URL
 */
export const extractFacebookPostId = (url: string): string | null => {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    
    // For Facebook video URLs like https://web.facebook.com/61577379871678/videos/1416324386238166
    const videoMatch = pathname.match(/\/videos\/(\d+)/);
    if (videoMatch) {
      return videoMatch[1];
    }
    
    // For Facebook post URLs
    const postMatch = pathname.match(/\/posts\/(\d+)/);
    if (postMatch) {
      return postMatch[1];
    }
    
    return null;
  } catch (error) {
    console.error('Error parsing Facebook URL:', error);
    return null;
  }
};

/**
 * Truncate text to specified length with ellipsis
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength).trim() + '...';
};

/**
 * Mock function to fetch Instagram content
 * In a real implementation, this would use Instagram's oEmbed API or Graph API
 */
export const fetchInstagramContent = async (url: string): Promise<SocialMediaContent | null> => {
  try {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const postId = extractInstagramPostId(url);
    if (!postId) return null;
    
    // Mock data - in real implementation, this would come from Instagram API
    const mockCaptions: Record<string, string> = {
      'DMVSUg0NCQk': 'Our latest initiative to combat drug abuse in local communities. Together we can make a difference! 🌟 #CommunityFirst #DrugPrevention #DAST',
      'DMVXCkQpcIh': 'Community outreach program making a real impact in Delta State. Proud to see our volunteers in action! 💪 #CommunityFirst #SocialImpact #DeltaState',
      'DMVbCP4Pfp0': 'Training our volunteers with the latest techniques to help those affected by substance abuse. Knowledge is power! 📚 #Training #Volunteers #SubstanceAbuse'
    };
    
    return {
      caption: mockCaptions[postId] || 'Check out our latest post on Instagram! Follow us for more updates.',
      likes: Math.floor(Math.random() * 2000) + 500,
      comments: Math.floor(Math.random() * 100) + 20,
      timestamp: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000), // Random time within last week
      username: 'dast.foundation',
      avatar: '/images/st.jpg'
    };
  } catch (error) {
    console.error('Error fetching Instagram content:', error);
    return null;
  }
};

/**
 * Mock function to fetch Facebook content
 * In a real implementation, this would use Facebook's Graph API
 */
export const fetchFacebookContent = async (url: string): Promise<SocialMediaContent | null> => {
  try {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const postId = extractFacebookPostId(url);
    if (!postId) return null;
    
    // Mock data - in real implementation, this would come from Facebook API
    const mockCaptions: Record<string, string> = {
      '1416324386238166': 'Important discussion on drug prevention strategies with community leaders. Building stronger communities together! 🤝 #DrugPrevention #CommunityLeaders',
      '722401620596428': 'Our team visiting local schools to educate students about the dangers of substance abuse. Education is prevention! 🎓 #SchoolVisit #Education #Prevention'
    };
    
    return {
      caption: mockCaptions[postId] || 'Watch our latest video on Facebook! Like and share to spread awareness.',
      likes: Math.floor(Math.random() * 1500) + 300,
      comments: Math.floor(Math.random() * 80) + 15,
      timestamp: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000), // Random time within last week
      username: 'DAST Foundation',
      avatar: '/images/st.jpg'
    };
  } catch (error) {
    console.error('Error fetching Facebook content:', error);
    return null;
  }
};

/**
 * Fetch social media content based on platform
 */
export const fetchSocialMediaContent = async (
  url: string, 
  platform: 'instagram' | 'facebook'
): Promise<SocialMediaContent | null> => {
  switch (platform) {
    case 'instagram':
      return fetchInstagramContent(url);
    case 'facebook':
      return fetchFacebookContent(url);
    default:
      return null;
  }
};
