# Contact Form Setup Instructions

The contact form has been enhanced with EmailJS integration and Google reCAPTCHA. Follow these steps to complete the setup.

## 📦 Required Packages

Install the necessary packages:

```bash
npm install @emailjs/browser react-google-recaptcha
npm install --save-dev @types/react-google-recaptcha
```

## 🔧 EmailJS Setup

### 1. Create EmailJS Account
1. Go to [EmailJS.com](https://www.emailjs.com/)
2. Sign up for a free account
3. Verify your email address

### 2. Create Email Service
1. In EmailJS dashboard, go to "Email Services"
2. Click "Add New Service"
3. Choose your email provider (Gmail, Outlook, etc.)
4. Follow the setup instructions
5. Note your **Service ID**

### 3. Create Email Template
1. Go to "Email Templates"
2. Click "Create New Template"
3. Use this template structure:

```
Subject: New Contact Form Submission - {{subject}}

From: {{from_name}} ({{from_email}})
Subject: {{subject}}

Message:
{{message}}

---
This message was sent via the MADAST contact form.
```

4. Note your **Template ID**

### 4. Get Public Key
1. Go to "Account" → "General"
2. Find your **Public Key**

## 🛡️ Google reCAPTCHA Setup

### 1. Create reCAPTCHA Site
1. Go to [Google reCAPTCHA](https://www.google.com/recaptcha/)
2. Click "Admin Console"
3. Click "+" to create a new site
4. Choose "reCAPTCHA v2" → "I'm not a robot" Checkbox
5. Add your domain (localhost for development)
6. Accept terms and submit

### 2. Get Site Key
1. Copy the **Site Key** from the reCAPTCHA admin panel

## 🔐 Environment Variables

### 1. Create .env File
Copy `.env.example` to `.env`:

```bash
cp .env.example .env
```

### 2. Add Your Credentials
Edit `.env` and replace the placeholder values:

```env
REACT_APP_EMAILJS_SERVICE_ID=service_xxxxxxx
REACT_APP_EMAILJS_TEMPLATE_ID=template_xxxxxxx
REACT_APP_EMAILJS_PUBLIC_KEY=xxxxxxxxxxxxxxx
REACT_APP_RECAPTCHA_SITE_KEY=6LeXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
```

## 🚀 Activate the Features

### 1. Uncomment EmailJS Import
In `src/pages/ContactPage.tsx`, uncomment:
```typescript
import emailjs from '@emailjs/browser';
```

### 2. Uncomment reCAPTCHA Import
```typescript
import ReCAPTCHA from 'react-google-recaptcha';
```

### 3. Update Configuration
In `src/pages/ContactPage.tsx`, replace the configuration with:
```typescript
import { EMAIL_CONFIG } from '../config/email';

// Use the imported config
const EMAILJS_SERVICE_ID = EMAIL_CONFIG.EMAILJS_SERVICE_ID;
const EMAILJS_TEMPLATE_ID = EMAIL_CONFIG.EMAILJS_TEMPLATE_ID;
const EMAILJS_PUBLIC_KEY = EMAIL_CONFIG.EMAILJS_PUBLIC_KEY;
const RECAPTCHA_SITE_KEY = EMAIL_CONFIG.RECAPTCHA_SITE_KEY;
```

### 4. Uncomment EmailJS Call
In the `handleSubmit` function, uncomment:
```typescript
await emailjs.send(EMAILJS_SERVICE_ID, EMAILJS_TEMPLATE_ID, templateParams, EMAILJS_PUBLIC_KEY);
```

And comment out the simulation:
```typescript
// await new Promise(resolve => setTimeout(resolve, 1000));
```

### 5. Uncomment reCAPTCHA Component
In the form JSX, uncomment:
```jsx
<ReCAPTCHA
  ref={recaptchaRef}
  sitekey={RECAPTCHA_SITE_KEY}
  theme="light"
/>
```

And remove the placeholder div.

## 🧪 Testing

### 1. Development Testing
1. Start the development server: `npm start`
2. Navigate to the contact page
3. Fill out the form and submit
4. Check your email for the message

### 2. reCAPTCHA Testing
1. Verify the reCAPTCHA appears
2. Complete the "I'm not a robot" challenge
3. Ensure form won't submit without reCAPTCHA

### 3. Error Handling
1. Test with invalid email addresses
2. Test with empty required fields
3. Test network errors (disconnect internet)

## 🔒 Security Notes

1. **Never commit .env file** - Add `.env` to `.gitignore`
2. **Use environment variables** - Never hardcode credentials
3. **Validate server-side** - Client-side validation is not enough
4. **Rate limiting** - Consider implementing rate limiting
5. **Spam protection** - reCAPTCHA helps but monitor for abuse

## 📧 Email Template Variables

The following variables are available in your EmailJS template:

- `{{from_name}}` - Full name (firstName + lastName)
- `{{from_email}}` - Sender's email address
- `{{subject}}` - Selected subject category
- `{{message}}` - Message content
- `{{to_name}}` - Recipient name (MADAST Team)

## 🎨 Customization

### Success/Error Messages
Modify the messages in the `handleSubmit` function:

```typescript
setSubmitMessage('Your custom success message here');
```

### Form Validation
Update validation rules in `src/config/email.ts`:

```typescript
export const FORM_VALIDATION = {
  // Add custom validation rules
};
```

### Styling
The form uses Tailwind CSS classes. Modify the className attributes to change appearance.

## 🆘 Troubleshooting

### Common Issues

1. **EmailJS not sending**
   - Check service ID, template ID, and public key
   - Verify email service is properly configured
   - Check browser console for errors

2. **reCAPTCHA not loading**
   - Verify site key is correct
   - Check domain is added to reCAPTCHA settings
   - Ensure internet connection

3. **Environment variables not working**
   - Restart development server after adding .env
   - Verify variable names start with REACT_APP_
   - Check .env file is in project root

### Debug Mode
Add console logs to debug:

```typescript
console.log('EmailJS Config:', {
  serviceId: EMAILJS_SERVICE_ID,
  templateId: EMAILJS_TEMPLATE_ID,
  // Don't log sensitive keys
});
```

## 📞 Support

If you need help:
1. Check the browser console for errors
2. Verify all credentials are correct
3. Test with a simple EmailJS example first
4. Check EmailJS and reCAPTCHA documentation
