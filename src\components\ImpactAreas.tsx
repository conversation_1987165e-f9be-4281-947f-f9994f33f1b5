import React from 'react';

interface ImpactAreaCardProps {
  icon: React.ReactNode; // Changed to ReactNode to allow for SVG icons later, but will pass string emojis for now
  title: string;
  description: string;
}

const ImpactAreaCard: React.FC<ImpactAreaCardProps> = ({ icon, title, description }) => {
  return (
    <div className="bg-white p-6 rounded-lg shadow-lg flex items-start space-x-4 h-full">
      <div className="flex-shrink-0">
        <div className="w-12 h-12 p-3 bg-indigo-100 rounded-lg text-indigo-600 inline-flex items-center justify-center text-2xl">
          {icon}
        </div>
      </div>
      <div className="flex flex-col flex-grow min-w-0"> {/* Added min-w-0 for flex-grow to work properly with text */}
        <h3 className="text-xl font-semibold mb-2 text-gray-800">{title}</h3>
        <p className="text-gray-600 text-sm mb-3 flex-grow">{description}</p>
        <a href="/programs" className="text-indigo-600 hover:text-indigo-800 font-semibold self-start mt-auto">
          Learn more →
        </a>
      </div>
    </div>
  );
};

const ImpactAreas: React.FC = () => {
  const areas = [
    {
      icon: '🛡️',
      title: 'Drug Prevention',
      description: 'Comprehensive programs to prevent drug abuse through education, community outreach, and support systems.',
    },
    {
      icon: '👥',
      title: 'Anti-Trafficking',
      description: 'Working against human trafficking through awareness campaigns and victim support.',
    },
    {
      icon: '📚',
      title: 'Education',
      description: 'Educational resources and materials for communities, schools, and families.',
    },
    {
      icon: '❤️',
      title: 'Support Services',
      description: 'Providing counseling, rehabilitation, and recovery support for those affected by addiction and trafficking.',
    },
    {
      icon: '🌍',
      title: 'Global Outreach',
      description: 'International partnerships and programs to combat these issues worldwide.',
    },
    {
      icon: '💡',
      title: 'Innovation',
      description: 'Developing new approaches and technologies to address modern challenges.',
    },
  ];

  return (
    <section className="py-12 bg-slate-50">
      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-800 title-underline-white mb-3">Our Impact Areas</h2> {/* Added underline class, removed text-gradient-animated */}
          <p className="text-gray-600 mt-2 max-w-2xl mx-auto"> 
            We work across multiple fronts to create lasting change in communities worldwide
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 relative z-10"> {/* Added relative z-10 */}
          {areas.map((area, index) => (
            <ImpactAreaCard key={index} icon={area.icon} title={area.title} description={area.description} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default ImpactAreas;
