import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

// Custom plugin to suppress source map warnings
const suppressSourceMapWarnings = () => {
  return {
    name: 'suppress-sourcemap-warnings',
    configureServer(server: any) {
      const originalWs = server.ws;
      server.ws = {
        ...originalWs,
        send(payload: any) {
          if (payload?.type === 'error' && payload?.err?.message?.includes('Failed to load source map')) {
            return; // Suppress source map warnings
          }
          originalWs.send(payload);
        }
      };
    }
  };
};

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    suppressSourceMapWarnings()
  ],
  optimizeDeps: {
    exclude: ['lucide-react'],
  },
  build: {
    sourcemap: false, // Disable source maps in production
    rollupOptions: {
      onwarn(warning, warn) {
        // Suppress source map warnings
        if (warning.code === 'SOURCEMAP_ERROR') return;
        warn(warning);
      }
    }
  },
  server: {
    // Suppress source map warnings for third-party packages
    fs: {
      strict: false,
    },
  },
});
