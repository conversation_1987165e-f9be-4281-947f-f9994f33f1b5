// Email and reCAPTCHA Configuration
// Replace these values with your actual EmailJS and reCAPTCHA credentials

export const EMAIL_CONFIG = {
  // EmailJS Configuration
  // Get these from https://www.emailjs.com/
  EMAILJS_SERVICE_ID: import.meta.env.VITE_APP_EMAILJS_SERVICE_ID || 'service_r23uhlm',
  EMAILJS_TEMPLATE_ID: import.meta.env.VITE_APP_EMAILJS_TEMPLATE_ID || 'template_2aizlcs',
  EMAILJS_PUBLIC_KEY: import.meta.env.VITE_APP_EMAILJS_PUBLIC_KEY || 'PmRFcrGk6Ph6Uj2dB',

  // Google reCAPTCHA Configuration
  // Get this from https://www.google.com/recaptcha/
  RECAPTCHA_SITE_KEY: import.meta.env.VITE_APP_RECAPTCHA_SITE_KEY || 'your_recaptcha_site_key',
};

// Email template parameters for EmailJS
export interface EmailTemplateParams {
  from_name: string;
  from_email: string;
  subject: string;
  message: string;
  to_name: string;
}

// Form validation rules
export const FORM_VALIDATION = {
  firstName: {
    required: true,
    minLength: 2,
    maxLength: 50,
  },
  lastName: {
    required: true,
    minLength: 2,
    maxLength: 50,
  },
  email: {
    required: true,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  },
  subject: {
    required: true,
  },
  message: {
    required: true,
    minLength: 10,
    maxLength: 1000,
  },
};
