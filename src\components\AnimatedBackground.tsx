import React, { useRef, useEffect } from 'react';
import * as THREE from 'three';

interface AnimatedBackgroundProps {
  className?: string;
  particleColor?: number; // e.g., 0xaaaaaa
  particleCount?: number;
  particleSize?: number;
}

const AnimatedBackground: React.FC<AnimatedBackgroundProps> = ({
  className,
  particleColor = 0xC4B5FD, // Default to violet-300
  particleCount = 400,      // Increased default
  particleSize = 0.05,      // Increased default
}) => {
  const mountRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!mountRef.current) return;

    const currentMount = mountRef.current;

    // Scene
    const scene = new THREE.Scene();

    // Camera
    const camera = new THREE.PerspectiveCamera(
      75,
      currentMount.clientWidth / currentMount.clientHeight,
      0.1,
      1000
    );
    camera.position.z = 5;

    // Renderer
    const renderer = new THREE.WebGLRenderer({ alpha: true }); // alpha: true for transparent background
    renderer.setSize(currentMount.clientWidth, currentMount.clientHeight);
    renderer.setPixelRatio(window.devicePixelRatio);
    currentMount.appendChild(renderer.domElement);

    // Particles
    const particlesGeometry = new THREE.BufferGeometry();
    const positions = new Float32Array(particleCount * 3);

    for (let i = 0; i < particleCount * 3; i += 3) {
      positions[i] = (Math.random() - 0.5) * 10; // x
      positions[i + 1] = (Math.random() - 0.5) * 10; // y
      positions[i + 2] = (Math.random() - 0.5) * 10; // z
    }

    particlesGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));

    const particlesMaterial = new THREE.PointsMaterial({
      color: particleColor,
      size: particleSize,
      transparent: true,
      opacity: 0.8, // Increased opacity for more prominence
      blending: THREE.AdditiveBlending, 
      depthWrite: false, // Helps with blending issues if particles overlap strangely
    });

    const particles = new THREE.Points(particlesGeometry, particlesMaterial);
    scene.add(particles);

    // Animation
    const clock = new THREE.Clock();

    const animate = () => {
      const elapsedTime = clock.getElapsedTime();

      // Update particles
      particles.rotation.y = elapsedTime * 0.05; // Slow rotation
      particles.rotation.x = elapsedTime * 0.02;

      // Move particles slowly and more visibly
      for (let i = 0; i < particleCount; i++) {
        const i3 = i * 3;
        const x = particlesGeometry.attributes.position.getX(i);
        // Make y movement a bit more pronounced and slightly faster
        particlesGeometry.attributes.position.setY(i, Math.sin(elapsedTime * 0.2 + x * 0.3) * 0.3 + positions[i3 + 1] * 0.0005); 
        // Add a slight z movement for more depth
        particlesGeometry.attributes.position.setZ(i, Math.cos(elapsedTime * 0.15 + x * 0.2) * 0.2 + positions[i3 + 2] * 0.0005);
      }
      particlesGeometry.attributes.position.needsUpdate = true;

      // Slightly rotate the whole particle system for more dynamism
      particles.rotation.y = elapsedTime * 0.08;
      particles.rotation.x = elapsedTime * 0.04;

      renderer.render(scene, camera);
      requestAnimationFrame(animate);
    };

    animate();

    // Handle resize
    const handleResize = () => {
      if (currentMount) {
        camera.aspect = currentMount.clientWidth / currentMount.clientHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(currentMount.clientWidth, currentMount.clientHeight);
      }
    };

    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
      if (currentMount && renderer.domElement) {
         currentMount.removeChild(renderer.domElement);
      }
      renderer.dispose();
      particlesGeometry.dispose();
      particlesMaterial.dispose();
    };
  }, [particleColor, particleCount, particleSize]);

  return <div ref={mountRef} className={className} style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%', zIndex: 0 }} />;
};

export default AnimatedBackground;
