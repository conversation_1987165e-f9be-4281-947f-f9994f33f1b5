export interface MediaPost {
  id: string;
  platform: 'instagram' | 'facebook';
  url: string;
  embedHtml?: string;
  caption: string;
  timestamp: Date;
  likes: number;
  comments: number;
  isVideo: boolean;
  thumbnail?: string;
  username: string;
  avatar?: string;
}

export interface TrendingVideo extends MediaPost {
  viewCount: number;
  duration: number;
  isTrending: true;
}

export interface MediaDataState {
  trendingVideo: TrendingVideo | null;
  instagramPosts: MediaPost[];
  facebookPosts: MediaPost[];
  loading: boolean;
  error: string | null;
}