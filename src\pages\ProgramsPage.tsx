import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, Users, BookOpen, <PERSON><PERSON><PERSON><PERSON>ke, ArrowRight } from 'lucide-react';

interface ProgramAreaItem {
  text: string;
}

interface ProgramAreaCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  items: ProgramAreaItem[];
  imageUrl?: string;
  imagePlaceholderText?: string; 
}

const ProgramAreaCard: React.FC<ProgramAreaCardProps> = ({ icon, title, description, items, imageUrl, imagePlaceholderText = "Program Image Placeholder" }) => (
  <div className="bg-white rounded-xl shadow-xl hover:shadow-2xl transition-shadow duration-300 overflow-hidden flex flex-col border border-gray-200/80">
    <div className="p-6 md:p-8 flex-grow">
      <div className="flex items-start mb-4">
        <div className="p-3 bg-primary-100 rounded-full mr-4 flex-shrink-0">{icon}</div>
        <div>
          <h3 className="text-xl lg:text-2xl font-semibold text-gray-800 mb-1">{title}</h3>
          <p className="text-sm text-gray-600 mb-4 leading-relaxed">{description}</p>
        </div>
      </div>
      <ul className="space-y-2 mb-5 pl-1">
        {items.map((item, index) => (
          <li key={index} className="flex items-start text-sm text-gray-700">
            <ArrowRight size={16} className="mr-2.5 mt-0.5 text-primary-500 flex-shrink-0" />
            <span>{item.text}</span>
          </li>
        ))}
      </ul>
    </div>
    {imageUrl ? (
      <img src={imageUrl} alt={title} className="w-full h-56 object-cover" /> 
    ) : (
      <div className="mt-auto bg-gray-100 aspect-[16/9] flex items-center justify-center border-t border-gray-200">
        <p className="text-gray-400 text-sm italic">{imagePlaceholderText}</p>
      </div>
    )}
  </div>
);

const programAreasData: ProgramAreaCardProps[] = [
  {
    icon: <ShieldAlert size={32} className="text-red-500" />,
    title: 'Drug Prevention',
    description: 'Comprehensive prevention programs targeting at-risk youth and communities.',
    items: [
      { text: 'School-based education initiatives' },
      { text: 'Community awareness campaigns' },
      { text: 'Peer-to-peer mentoring programs' },
      { text: 'Family engagement and support' },
    ],
    imageUrl: '/images/young-depressed-adult-home.jpg',
  },
  {
    icon: <Users size={32} className="text-purple-500" />,
    title: 'Anti-Trafficking',
    description: 'Combating human trafficking through awareness, prevention, and victim support.',
    items: [
      { text: 'Public awareness campaigns' },
      { text: 'Victim identification protocols' },
      { text: 'Safe house and shelter support' },
      { text: 'Access to legal assistance' },
    ],
    imageUrl: '/images/black-girl-with-sadness-emotion.jpg',
  },

  {
    icon: <HeartHandshake size={32} className="text-pink-500" />,
    title: 'Support Services',
    description: 'Comprehensive support for survivors and affected families.',
    items: [
      { text: 'Individual and group counseling' },
      { text: 'Holistic recovery programs' },
      { text: 'Support groups for families' },
      { text: 'Reintegration and aftercare assistance' },
    ],
    imageUrl: '/images/man-home-feeling-exhausted-from-lack-sleep-suffering-headache.jpg',
  },
];

// --- Program Details Tabs Section ---

interface ProgramDetailTabContent {
  tabTitle: string;
  contentTitle: string;
  description: string;
  items: string[];
  imageUrl?: string;
  imagePlaceholder?: string;
}

const programDetailsContent: ProgramDetailTabContent[] = [
  {
    tabTitle: 'Prevention Programs',
    contentTitle: 'Proactive Drug Abuse Prevention',
    description: 'Our prevention programs focus on education, awareness, and building resilience in communities to prevent drug abuse before it starts, empowering individuals with knowledge and skills.',
    items: [
      'Evidence-based school prevention curricula',
      'Engaging community awareness campaigns',
      'Educational resources for parents and families',
      'Effective peer mentorship initiatives',
    ],
    imageUrl: '/images/image34.jpg',
  },
  {
    tabTitle: 'Intervention Services',
    contentTitle: 'Responsive Early Intervention',
    description: "When prevention isn't enough, our intervention services provide immediate, compassionate support and guidance to individuals and families facing crisis.",
    items: [
      '24/7 crisis intervention services',
      'Confidential emergency support hotline',
      'Timely referral to specialized treatment',
      'Comprehensive family crisis support',
    ],
    imageUrl: '/images/image35.jpg',
  },
  {
    tabTitle: 'Recovery Support',
    contentTitle: 'Holistic Recovery & Rehabilitation',
    description: 'Our recovery programs provide long-term, multifaceted support for individuals on their journey to healing, restoration, and sustained well-being.',
    items: [
      'Personalized individual & group counseling',
      'Structured residential treatment programs',
      'Vocational support and job placement aid',
      'Continuous aftercare and relapse prevention',
    ],
    imageUrl: '/images/image37.jpg',
  },
];

const ProgramDetailsTabs: React.FC = () => {
  const [activeTab, setActiveTab] = React.useState<string>(programDetailsContent[0].tabTitle);

  const currentTabData = programDetailsContent.find(tab => tab.tabTitle === activeTab);

  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-6">
        <div className="text-center mb-14 md:mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-800 title-underline-white mb-3">Program Details</h2> {/* Removed text-gradient-animated */}
          <p className="text-lg text-gray-600 mt-2 max-w-2xl mx-auto leading-relaxed">
            Learn more about our specific program offerings and how they make a difference.
          </p>
        </div>

        <div className="mb-10 flex justify-center">
          <div className="bg-gray-200 p-1.5 rounded-xl shadow-md flex flex-wrap justify-center space-x-1">
            {programDetailsContent.map((tab) => (
              <button
                key={tab.tabTitle}
                onClick={() => setActiveTab(tab.tabTitle)}
                className={`px-5 py-2.5 my-1 rounded-lg text-sm font-semibold transition-all duration-200 ease-in-out
                  ${activeTab === tab.tabTitle 
                    ? 'bg-primary-600 text-white shadow-lg transform scale-105' 
                    : 'text-gray-700 hover:bg-primary-100 hover:text-primary-700'
                  }`}
              >
                {tab.tabTitle}
              </button>
            ))}
          </div>
        </div>

        {currentTabData && (
          <div className="bg-white p-6 md:p-10 rounded-xl shadow-xl border border-gray-200/70">
            <div className="flex flex-col md:flex-row gap-8 lg:gap-12 items-start">
              <div className="md:w-1/2">
                <h3 className="text-2xl lg:text-3xl font-semibold text-primary-700 mb-4">{currentTabData.contentTitle}</h3>
                <p className="text-gray-600 mb-6 leading-relaxed">{currentTabData.description}</p>
                <ul className="space-y-2.5 pl-1">
                  {currentTabData.items.map((item, index) => (
                    <li key={index} className="flex items-start text-gray-700">
                      <ArrowRight size={18} className="mr-2.5 mt-1 text-primary-500 flex-shrink-0" />
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
              <div className="md:w-1/2 mt-6 md:mt-0 w-full">
                {currentTabData.imageUrl ? (
                  <img src={currentTabData.imageUrl} alt={currentTabData.contentTitle} className="w-full h-auto aspect-[16/9] rounded-lg object-cover shadow-md border border-gray-200" />
                ) : (
                  <div className="bg-gray-100 aspect-[16/9] rounded-lg flex items-center justify-center border border-gray-200">
                    <p className="text-gray-400 italic">{currentTabData.imagePlaceholder || "Image coming soon"}</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  );
};


const ProgramsPage: React.FC = () => {
  return (
    <>
      {/* Hero Section */}
      <section className="relative text-white py-24 md:py-32 bg-cover bg-center" style={{ backgroundImage: "url('/images/a2.jpg')" }}>
        <div className="absolute inset-0 bg-black opacity-50"></div> {/* Overlay for contrast */}
        <div className="container mx-auto px-6 text-center relative z-10"> {/* Ensure content is above overlay */}
          <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold mb-6 leading-tight" style={{ textShadow: '2px 2px 4px rgba(0,0,0,0.5)' }}>Our Programs</h1> {/* Adjusted shadow */}
          <p className="text-lg md:text-xl max-w-3xl mx-auto text-neutral-100 leading-relaxed" style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.3)' }}> {/* Adjusted shadow */}
            Comprehensive programs designed to prevent, intervene, and support recovery from drug abuse and human trafficking.
          </p>
          </div>
        </section>

        {/* Program Areas Section */}
        <section className="py-16 md:py-24 bg-slate-50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-14 md:mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-800 title-underline-white mb-3">Program Areas</h2> {/* Removed text-gradient-animated */}
              <p className="text-lg text-gray-600 mt-2 max-w-2xl mx-auto leading-relaxed">
                Our comprehensive approach addresses every stage of the crisis, from prevention to recovery.
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-10">
              {programAreasData.map((program) => (
                <ProgramAreaCard
                  key={program.title}
                  icon={program.icon}
                  title={program.title}
                  description={program.description}
                  items={program.items}
                  imageUrl={program.imageUrl}
                />
              ))}
            </div>
          </div>
        </section>
      <ProgramDetailsTabs />
    </>
  );
};

export default ProgramsPage;
