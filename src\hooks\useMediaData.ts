import { useState, useEffect } from 'react';
import { MediaPost, TrendingVideo, MediaDataState } from '../types/media';

// Mock data for development
const MOCK_TRENDING_VIDEO: TrendingVideo = {
  id: '1',
  platform: 'instagram',
  url: 'https://www.instagram.com/dast.foundation/reel/DMVSUg0NCQk/',
  caption: 'Our latest initiative to combat drug abuse in local communities. Together we can make a difference!',
  timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
  likes: 1250,
  comments: 87,
  isVideo: true,
  thumbnail: '/images/a1.jpg',
  username: 'dast.foundation',
  avatar: '/images/st.jpg',
  viewCount: 12500,
  duration: 60,
  isTrending: true
};

const MOCK_INSTAGRAM_POSTS: MediaPost[] = [
  {
    id: '2',
    platform: 'instagram',
    url: 'https://www.instagram.com/dast.foundation/reel/DMVXCkQpcIh/',
    caption: 'Community outreach program making a real impact in Delta State. #CommunityFirst #SocialImpact',
    timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000), // 5 hours ago
    likes: 892,
    comments: 42,
    isVideo: true,
    thumbnail: '/images/a2.jpg',
    username: 'dast.foundation',
    avatar: '/images/st.jpg'
  },
  {
    id: '3',
    platform: 'instagram',
    url: 'https://www.instagram.com/dast.foundation/reel/DMVbCP4Pfp0/',
    caption: 'Training our volunteers with the latest techniques to help those affected by substance abuse.',
    timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
    likes: 1103,
    comments: 65,
    isVideo: true,
    thumbnail: '/images/a3.jpg',
    username: 'dast.foundation',
    avatar: '/images/st.jpg'
  },
  {
    id: '4',
    platform: 'instagram',
    url: 'https://www.instagram.com/dast.foundation/reel/DMZT4D-sK3-/',
    caption: 'Meeting with local leaders to discuss strategies for preventing sex trafficking.',
    timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
    likes: 765,
    comments: 38,
    isVideo: true,
    thumbnail: '/images/a4.jpg',
    username: 'dast.foundation',
    avatar: '/images/st.jpg'
  }
];

const MOCK_FACEBOOK_POSTS: MediaPost[] = [
  {
    id: '5',
    platform: 'facebook',
    url: 'https://web.facebook.com/61577379871678/videos/1416324386238166',
    caption: 'Important discussion on drug prevention strategies with community leaders.',
    timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000), // 3 hours ago
    likes: 542,
    comments: 29,
    isVideo: true,
    thumbnail: '/images/a5.jpg',
    username: 'DAST Foundation',
    avatar: '/images/st.jpg'
  },
  {
    id: '6',
    platform: 'facebook',
    url: 'https://web.facebook.com/61577379871678/videos/722401620596428',
    caption: 'Our team visiting local schools to educate students about the dangers of substance abuse.',
    timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
    likes: 876,
    comments: 53,
    isVideo: true,
    thumbnail: '/images/bishop.jpg',
    username: 'DAST Foundation',
    avatar: '/images/st.jpg'
  }
];

export const useMediaData = (): MediaDataState => {
  const [state, setState] = useState<MediaDataState>({
    trendingVideo: null,
    instagramPosts: [],
    facebookPosts: [],
    loading: true,
    error: null
  });

  useEffect(() => {
    const fetchMediaData = async () => {
      try {
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // In a real implementation, you would fetch data from APIs here
        // For now, we're using mock data
        setState({
          trendingVideo: MOCK_TRENDING_VIDEO,
          instagramPosts: MOCK_INSTAGRAM_POSTS,
          facebookPosts: MOCK_FACEBOOK_POSTS,
          loading: false,
          error: null
        });
      } catch (error) {
        console.error('Error fetching media data:', error);
        setState({
          trendingVideo: null,
          instagramPosts: [],
          facebookPosts: [],
          loading: false,
          error: 'Failed to load media content. Please try again later.'
        });
      }
    };

    fetchMediaData();
  }, []);

  return state;
};