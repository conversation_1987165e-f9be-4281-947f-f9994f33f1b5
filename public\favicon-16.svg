<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" width="16" height="16">
  <defs>
    <radialGradient id="globeGradient16" cx="0.3" cy="0.3" r="0.8">
      <stop offset="0%" style="stop-color:#4F46E5;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#2563EB;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </radialGradient>
    <linearGradient id="continentGradient16" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10B981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle (globe) -->
  <circle cx="8" cy="8" r="7" fill="url(#globeGradient16)" />
  
  <!-- Simplified continents for small size -->
  <g fill="url(#continentGradient16)" opacity="0.8">
    <path d="M4 5 Q6 4 7 5 Q8 6 7 7 Q6 8 4 7 Q3 6 4 5 Z" />
    <path d="M9 4 Q11 3 12 5 Q13 6 12 8 Q11 9 9 8 Q8 7 9 6 Q9 5 9 4 Z" />
    <path d="M5 10 Q6 9 7 11 Q7 12 6 13 Q5 13 4 12 Q3 11 4 10 Q4 10 5 10 Z" />
  </g>
  
  <!-- Highlight -->
  <ellipse cx="6" cy="6" rx="2" ry="3" fill="rgba(255,255,255,0.2)" opacity="0.6" />
</svg>
