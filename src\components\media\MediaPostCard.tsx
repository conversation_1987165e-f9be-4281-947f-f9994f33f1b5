import React, { useState } from 'react';
import { MediaPost } from '../../types/media';
import MediaEmbed from './MediaEmbed';

interface MediaPostCardProps {
  post: MediaPost;
}

const MediaPostCard: React.FC<MediaPostCardProps> = ({ post }) => {
  const [expanded, setExpanded] = useState(false);
  const [embedError, setEmbedError] = useState(false);

  const formatCount = (count: number): string => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    }
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  const formatDate = (date: Date): string => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor(diffMs / (1000 * 60));

    if (diffDays > 0) {
      return `${diffDays}d ago`;
    }
    if (diffHours > 0) {
      return `${diffHours}h ago`;
    }
    if (diffMinutes > 0) {
      return `${diffMinutes}m ago`;
    }
    return 'Just now';
  };

  const toggleCaption = () => {
    setExpanded(!expanded);
  };

  const handleEmbedError = () => {
    setEmbedError(true);
  };

  const getPlatformColor = () => {
    return post.platform === 'instagram' 
      ? 'from-yellow-400 to-pink-600' 
      : 'bg-blue-600';
  };

  const getPlatformIcon = () => {
    if (post.platform === 'instagram') {
      return (
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
        </svg>
      );
    } else {
      return (
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
          <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
        </svg>
      );
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 border border-gray-200">
      {/* Post Header */}
      <div className="p-4 flex items-center">
        <div className="relative">
          <img 
            src={post.avatar || '/images/st.jpg'} 
            alt={post.username}
            className="w-10 h-10 rounded-full"
          />
          <div className={`absolute -bottom-1 -right-1 w-5 h-5 rounded-full bg-gradient-to-r ${getPlatformColor()} flex items-center justify-center`}>
            <span className="text-white text-xs">
              {getPlatformIcon()}
            </span>
          </div>
        </div>
        <div className="ml-3">
          <h3 className="font-semibold text-gray-800">{post.username}</h3>
          <p className="text-xs text-gray-500 flex items-center">
            {formatDate(post.timestamp)}
            <span className="mx-1">•</span>
            <span className="capitalize">{post.platform}</span>
          </p>
        </div>
      </div>

      {/* Media Embed or Thumbnail */}
      <div className="relative w-full aspect-square bg-gray-100">
        {embedError || !post.isVideo ? (
          <div className="w-full h-full flex items-center justify-center">
            <img 
              src={post.thumbnail || '/images/a1.jpg'} 
              alt={post.caption}
              className="w-full h-full object-cover"
            />
            {post.isVideo && (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="bg-black/50 rounded-full p-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-white" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            )}
          </div>
        ) : (
          <MediaEmbed
            url={post.url}
            platform={post.platform}
            showCaption={false}
            onError={handleEmbedError}
          />
        )}
      </div>

      {/* Post Content */}
      <div className="p-4">
        <p className={`text-gray-700 ${expanded ? '' : 'line-clamp-3'}`}>
          {post.caption}
          {post.caption.length > 100 && (
            <button
              onClick={toggleCaption}
              className="ml-1 text-blue-500 hover:text-blue-700 font-medium"
            >
              {expanded ? 'Show less' : 'See more'}
            </button>
          )}
        </p>

      </div>

      {/* Engagement Metrics */}
      <div className="flex justify-between mt-4 pt-4 border-t border-gray-100 px-4 pb-4">
        <div className="flex items-center text-gray-500">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
          </svg>
          <span>{formatCount(post.likes)}</span>
        </div>
        <div className="flex items-center text-gray-500">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M18 5v8a2 2 0 01-2 2h-5l-5 4v-4H4a2 2 0 01-2-2V5a2 2 0 012-2h12a2 2 0 012 2zM7 8H5v2h2V8zm2 0h2v2H9V8zm6 0h-2v2h2V8z" clipRule="evenodd" />
          </svg>
          <span>{formatCount(post.comments)}</span>
        </div>
        <div className="flex items-center text-gray-500">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path d="M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z" />
          </svg>
          <span>Share</span>
        </div>
      </div>
    </div>
  );
};

export default MediaPostCard;