import React, { useState } from 'react';
import { MediaPost } from '../../types/media';
import MediaPostCard from './MediaPostCard';

interface SocialMediaFeedProps {
  instagramPosts: MediaPost[];
  facebookPosts: MediaPost[];
  loading: boolean;
}

const SocialMediaFeed: React.FC<SocialMediaFeedProps> = ({ 
  instagramPosts, 
  facebookPosts, 
  loading 
}) => {
  const [activeTab, setActiveTab] = useState<'all' | 'instagram' | 'facebook'>('all');

  const filteredPosts = () => {
    if (activeTab === 'instagram') {
      return instagramPosts;
    }
    if (activeTab === 'facebook') {
      return facebookPosts;
    }
    return [...instagramPosts, ...facebookPosts].sort((a, b) => 
      b.timestamp.getTime() - a.timestamp.getTime()
    );
  };

  const renderSkeletonCards = () => {
    return Array.from({ length: 6 }).map((_, index) => (
      <div key={index} className="bg-white rounded-xl shadow-md overflow-hidden animate-pulse">
        <div className="bg-gray-300 h-48 w-full"></div>
        <div className="p-4">
          <div className="flex items-center mb-3">
            <div className="bg-gray-300 rounded-full h-10 w-10 mr-3"></div>
            <div>
              <div className="h-4 bg-gray-300 rounded w-24 mb-2"></div>
              <div className="h-3 bg-gray-300 rounded w-16"></div>
            </div>
          </div>
          <div className="h-4 bg-gray-300 rounded w-full mb-2"></div>
          <div className="h-4 bg-gray-300 rounded w-3/4"></div>
        </div>
      </div>
    ));
  };

  return (
    <section>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800">Latest from Social Media</h2>
        
        {/* Tab Navigation */}
        <div className="flex space-x-2 bg-gray-100 p-1 rounded-lg">
          <button
            onClick={() => setActiveTab('all')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition ${
              activeTab === 'all'
                ? 'bg-white text-gray-900 shadow'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            All Posts
          </button>
          <button
            onClick={() => setActiveTab('instagram')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition flex items-center ${
              activeTab === 'instagram'
                ? 'bg-white text-gray-900 shadow'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <span className="w-4 h-4 bg-gradient-to-r from-yellow-400 to-pink-600 rounded mr-2"></span>
            Instagram
          </button>
          <button
            onClick={() => setActiveTab('facebook')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition flex items-center ${
              activeTab === 'facebook'
                ? 'bg-white text-gray-900 shadow'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <span className="w-4 h-4 bg-blue-600 rounded mr-2"></span>
            Facebook
          </button>
        </div>
      </div>

      {/* Posts Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {loading ? (
          renderSkeletonCards()
        ) : filteredPosts().length > 0 ? (
          filteredPosts().map((post) => (
            <MediaPostCard key={post.id} post={post} />
          ))
        ) : (
          <div className="col-span-full text-center py-12">
            <p className="text-gray-500">No posts found.</p>
          </div>
        )}
      </div>
    </section>
  );
};

export default SocialMediaFeed;