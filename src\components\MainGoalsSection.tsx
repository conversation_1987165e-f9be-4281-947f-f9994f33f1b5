import React from 'react';
import { Link } from 'react-router-dom'; // Assuming you use react-router-dom for navigation

const MainGoalsSection: React.FC = () => {
  const goals = [
    {
      title: 'Stopping Drug Abuse',
      subtitle: 'Dedicated to eradicating drug abuse and supporting recovery.',
      imageUrl: '/images/dg1.jpg', // Ensure this path is correct relative to the public folder
      linkTo: '/programs', // Link to the programs page
    },
    {
      title: 'Combating Sex Trafficking',
      subtitle: 'Committed to ending sex trafficking and aiding survivors.',
      imageUrl: '/images/st.jpg', // Ensure this path is correct relative to the public folder
      linkTo: '/programs', // Link to the programs page
    },
  ];

  return (
    <section className="py-12 md:py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-800 mb-10 md:mb-16">Our Main Focus</h2>
        <div className="grid md:grid-cols-2 gap-8">
          {goals.map((goal, index) => (
            <div
              key={index}
              className="group relative rounded-lg overflow-hidden shadow-xl transform transition-all duration-500 hover:scale-105"
              style={{ minHeight: '400px' }}
            >
              <div
                className="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110"
                style={{ backgroundImage: `url(${goal.imageUrl})` }}
              ></div>
              <div className="absolute inset-0 bg-black bg-opacity-50 group-hover:bg-opacity-60 transition-opacity duration-300"></div>
              <div className="relative z-10 flex flex-col items-center justify-center text-center p-8 h-full">
                <h3 className="text-3xl lg:text-4xl font-bold text-white mb-3">{goal.title}</h3>
                <p className="text-lg text-gray-200 mb-6">{goal.subtitle}</p>
                <Link
                  to={goal.linkTo}
                  className="absolute bottom-8 opacity-0 group-hover:opacity-100 group-hover:bottom-12 transform translate-y-4 group-hover:translate-y-0 transition-all duration-500 ease-in-out bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg shadow-md"
                >
                  Learn More
                </Link>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default MainGoalsSection;
