import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Home from './pages/Home';
import AboutPage from './pages/AboutPage';
import ProgramsPage from './pages/ProgramsPage';
import GetInvolvedPage from './pages/GetInvolvedPage'; // Import the new GetInvolvedPage
import ResourcesPage from './pages/ResourcesPage';
import ContactPage from './pages/ContactPage'; // Import the new ContactPage
import MediaPage from './pages/MediaPage'; // Import the new MediaPage
import Header from './components/Header';
import Footer from './components/Footer';
import CallToAction from './components/CallToAction';
import './styles/variables.css';
// import AnimatedBackground from './components/AnimatedBackground'; // Removed AnimatedBackground

function App() {
  return (
    <Router>
      {/* <AnimatedBackground className="fixed top-0 left-0 w-full h-full -z-10" particleColor={0x6D28D9} particleCount={200} particleSize={0.03} /> */} {/* Adjusted particleColor to a darker purple, reduced count and size */}
      <div className="font-sans flex flex-col min-h-screen">
        <Header />
        <main className="flex-grow">
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/about" element={<AboutPage />} />
            <Route path="/programs" element={<ProgramsPage />} />
            <Route path="/get-involved" element={<GetInvolvedPage />} /> {/* Add route for GetInvolvedPage */}
            <Route path="/resources" element={<ResourcesPage />} />
            <Route path="/contact" element={<ContactPage />} /> {/* Add route for ContactPage */}
            <Route path="/media" element={<MediaPage />} /> {/* Add route for MediaPage */}
          </Routes>
        </main>
        <CallToAction />
        <Footer />
      </div>
    </Router>
  );
}

export default App;
