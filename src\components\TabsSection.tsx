import React, { useState } from 'react';
import { TabContent } from '../types/types';
import AnimatedBack<PERSON> from './AnimatedBackground'; // Import the new component

const tabsData: TabContent[] = [
  {
    id: 'mission',
    title: 'Our Mission',
    content: `
      <h3 class="text-2xl font-semibold text-neutral-800 mb-4">Empowering Faith, Ending Exploitation</h3>
      <p class="text-neutral-700 leading-relaxed mb-4">
        At the heart of our endeavor is a commitment to <strong class="font-semibold text-primary-700">equip and empower</strong> religious leaders and their communities. We provide comprehensive resources designed to effectively identify the signs of abuse, offer compassionate support to survivors, and champion systemic change.
      </p>
      <p class="text-neutral-700 leading-relaxed mb-4">
        Our <strong class="font-semibold text-primary-700">interfaith approach</strong> is pivotal, fostering robust coalitions that transcend denominational lines to unite against shared challenges. Through targeted education and proactive community engagement, we are dedicated to cultivating environments where domestic abuse, sexual exploitation, and human trafficking find no quarter.
      </p>
      <blockquote class="border-l-4 border-primary-500 pl-4 py-2 my-6 bg-primary-50/50 rounded-r-md">
        <p class="italic text-neutral-600">
          "Our ultimate goal is to mobilize the unique and trusted position of faith leaders, transforming them into powerful advocates who protect the vulnerable and forge pathways to lasting, positive change within society."
        </p>
      </blockquote>
      <p class="text-neutral-700 leading-relaxed">
        We believe that by working together, faith communities can be a formidable force in creating a safer, more just world for everyone.
      </p>
    `
  },

  {
    id: 'partnerships',
    title: 'Partnerships',
    content: `
      <p class="text-neutral-700 mb-6">Our work is strengthened through collaborative partnerships with leading organizations dedicated to justice and support. These alliances amplify our impact and extend our reach.</p>
      <div class="grid grid-cols-1 sm:grid-cols-2 gap-x-8 gap-y-4">
        <div class="flex items-center p-3 bg-white/50 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300">
          <span class="text-primary-600 text-xl mr-3">🤝</span>
          <span class="text-neutral-600">National Coalition Against Domestic Violence</span>
        </div>
        <div class="flex items-center p-3 bg-white/50 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300">
          <span class="text-primary-600 text-xl mr-3">🕊️</span>
          <span class="text-neutral-600">Interfaith Alliance for Justice</span>
        </div>
        <div class="flex items-center p-3 bg-white/50 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300">
          <span class="text-primary-600 text-xl mr-3">⚖️</span>
          <span class="text-neutral-600">Human Trafficking Legal Center</span>
        </div>
        <div class="flex items-center p-3 bg-white/50 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300">
          <span class="text-primary-600 text-xl mr-3">🛡️</span>
          <span class="text-neutral-600">Faith Trust Institute</span>
        </div>
        <div class="flex items-center p-3 bg-white/50 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 sm:col-span-2">
          <span class="text-primary-600 text-xl mr-3">🏡</span>
          <span class="text-neutral-600">Safe Havens Interfaith Partnership Against Domestic Violence</span>
        </div>
      </div>
      <p class="mt-8 text-center text-neutral-700">Together, we can create lasting change. <a href="#" class="text-primary-600 hover:text-primary-700 font-semibold">Join our network</a> of partners committed to ending abuse and exploitation.</p>
    `
  }
];

const TabsSection: React.FC = () => {
  const [activeTab, setActiveTab] = useState(tabsData[0].id);

  return (
    <section className="py-16 bg-white relative overflow-hidden">
      <AnimatedBackground particleColor={0xC4B5FD} particleCount={400} particleSize={0.05} />
      <div className="container mx-auto px-4 relative z-10"> {/* Removed text-center from here */}
        <div className="text-center"> {/* Added a dedicated centering div for the title */}
          <h2 className="text-5xl font-bold mb-16 text-gray-800 title-underline-white">Our Focus</h2> {/* Changed text-4xl to text-5xl, removed text-gradient-animated */}
        </div>
        
        <div className="max-w-4xl mx-auto text-left"> 
          {/* Tabs navigation */}
          <div className="flex flex-wrap justify-center gap-2 mb-8 border-b border-neutral-300"> 
            {tabsData.map((tab) => (
              <button
                key={tab.id}
                className={`px-5 py-3 font-medium transition-all duration-300 relative ${
                  activeTab === tab.id
                    ? 'text-primary-700' // Darker primary for contrast on white
                    : 'text-neutral-600 hover:text-neutral-900' // Adjusted text colors
                }`}
                onClick={() => setActiveTab(tab.id)}
              >
                {tab.title}
                {activeTab === tab.id && (
                  <span className="absolute bottom-0 left-0 w-full h-0.5 bg-primary-700 transform translate-y-0.5"></span>
                )}
              </button>
            ))}
          </div>
          
          {/* Tab content */}
          <div className="bg-neutral-50/80 backdrop-blur-lg p-8 md:p-10 rounded-2xl shadow-xl"> {/* Increased rounding, padding, blur, shadow and opacity */}
            {tabsData.map((tab) => (
              <div
                key={tab.id}
                className={`transition-all duration-500 ${
                  activeTab === tab.id
                    ? 'opacity-100 translate-y-0'
                    : 'opacity-0 absolute -z-10 translate-y-4'
                }`}
                dangerouslySetInnerHTML={{ __html: tab.content }}
              ></div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default TabsSection;
