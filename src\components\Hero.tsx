import React from 'react';

const Hero: React.FC = () => {

  return (
    <section
      className="relative pt-24 pb-20 md:pt-32 md:pb-32 text-white overflow-hidden"
      style={{ clipPath: 'polygon(0 0, 100% 0, 100% 85%, 0 100%)' }}
    >
      <video
        autoPlay
        loop
        muted
        playsInline
        className="absolute inset-0 w-full h-full object-cover -z-10"
      >
        <source src="/videos/newbg.mp4" type="video/mp4" />
        Your browser does not support the video tag.
      </video>
      <div
        className="absolute inset-0 bg-black/50" // Overlay for text contrast
      ></div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-3xl mx-auto text-center">
          <h1
            className="text-4xl md:text-5xl font-bold mb-6 leading-tight pt-[30px]"
          >
            Faith and Secular Leaders with their communities partnering against abuse and exploitation!
          </h1>
          <p className="text-lg md:text-xl mb-8 text-neutral-100 max-w-4xl mx-auto">
            Enabling faith and secular leaders, social workers, teachers, counsellors, students, parents, peer groups, and volunteers among others to rescue and transform the lives of victims of drug abuse and sex trafficking through education, advocacy campaigns, enlightenment conferences, intervention programs, outreach works, and community actions.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="#"
              className="cta-primary"
            >
              Get Involved
            </a>
            <a
              href="#"
              className="cta-secondary"
            >
              Learn More
            </a>
          </div>
        </div>
      </div>

      {/* <div className="absolute bottom-0 left-0 w-full h-16 bg-white" style={{ clipPath: 'polygon(0 100%, 100% 0, 100% 100%, 0% 100%)' }}></div> */}
    </section>
  );
};

export default Hero;
