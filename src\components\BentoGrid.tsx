import React from 'react';
import { ProgramCard } from '../types/types';
import { Users, FileText, BookOpen, Globe } from 'lucide-react';

const programsData: ProgramCard[] = [

  {
    id: 'survivor-support',
    title: 'Survivor Support Networks',
    description: 'Safe housing partnerships and spiritual counseling services for survivors of abuse and trafficking.',
    icon: 'Users',
    size: 'medium',
    backgroundImage: '/images/a5.jpg',
  },
  {
    id: 'policy-advocacy',
    title: 'Policy Advocacy',
    description: 'Equipping faith leaders with tools to advocate for stronger protections and better support systems at local and national levels.',
    icon: 'FileText',
    size: 'medium',
    backgroundImage: '/images/a2.jpg',
  },
  {
    id: 'community-awareness',
    title: 'Community Awareness',
    description: 'Educational materials and workshop frameworks to raise awareness about domestic abuse and trafficking in congregations.',
    icon: 'Globe',
    size: 'medium',
    backgroundImage: '/images/a4.jpg',
  }
];

const BentoGrid: React.FC = () => {
  const getIcon = (iconName: string) => {
    switch (iconName) {
      case 'Users':
        return <Users className="h-7 w-7 text-white" />; // Adjusted icon color
      case 'FileText':
        return <FileText className="h-7 w-7 text-white" />; // Adjusted icon color
      case 'BookOpen':
        return <BookOpen className="h-7 w-7 text-white" />; // Adjusted icon color
      case 'Globe':
        return <Globe className="h-7 w-7 text-white" />; // Adjusted icon color
      default:
        return <Globe className="h-7 w-7 text-white" />; // Adjusted icon color
    }
  };

  return (
    <section className="py-16 bg-neutral-100"> {/* Slightly darker background for section */}
      <div className="container mx-auto px-4 text-center"> {/* Added text-center to parent */}
        <h2 className="text-4xl font-bold text-gray-800 title-underline-white mb-3">Our Core Initiatives</h2> {/* Removed text-center from h2, removed text-gradient-animated */}
        <p className="text-center text-neutral-600 max-w-2xl mx-auto mb-12 mt-2">
          Comprehensive programs designed to equip faith communities with the tools, knowledge, and support networks needed to combat exploitation.
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-5xl mx-auto">
          {programsData.map((program) => {
            return (
              <div
                key={program.id}
                className="rounded-xl shadow-lg overflow-hidden group hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1.5 relative min-h-[350px] md:min-h-[380px] flex flex-col"
                style={{ backgroundImage: `url(${program.backgroundImage})`, backgroundSize: 'cover', backgroundPosition: 'center' }}
              >
                {/* Overlay for text contrast */}
                <div className="absolute inset-0 bg-black/60 group-hover:bg-black/70 transition-colors duration-300"></div>
                
                {/* Content container */}
                <div className="p-6 md:p-8 h-full flex flex-col relative z-10 text-white"> {/* Text color to white */}
                  <div className="flex items-center mb-4">
                    <div className="p-3 rounded-lg bg-white/20 backdrop-blur-sm ring-1 ring-white/30"> {/* Icon background with blur */}
                      {getIcon(program.icon)}
                    </div>
                    <h3 className="ml-4 text-xl md:text-2xl font-semibold">{program.title}</h3> {/* Adjusted text size */}
                  </div>
                  <p className="text-neutral-200 flex-grow text-sm md:text-base leading-relaxed">{program.description}</p> {/* Adjusted text color and size */}
                  <a 
                    href="/programs" // Updated link
                    className="mt-5 inline-flex items-center text-white font-semibold group-hover:text-primary-300 transition-colors duration-300 self-start py-2 px-4 rounded-md bg-white/10 hover:bg-white/20 backdrop-blur-sm"
                  >
                    Learn more
                    <svg 
                      className="ml-1.5 w-4 h-4 transition-transform duration-300 group-hover:translate-x-1" 
                      fill="none" 
                      viewBox="0 0 24 24" 
                      stroke="currentColor"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                  </a>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default BentoGrid;
