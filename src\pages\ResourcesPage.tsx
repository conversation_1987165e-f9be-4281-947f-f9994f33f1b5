import React from 'react';
import { AlertTriangle, Phone, BookOpen, Download, ExternalLink, LifeBuoy, FileText, Users } from 'lucide-react'; // Added more icons

const ResourcesPage: React.FC = () => {
  return (
    <>
      {/* Hero Section - Enhanced */}
      <section className="relative text-white py-24 md:py-32 bg-cover bg-center" style={{ backgroundImage: "url('/images/a3.jpg')" }}>
        <div className="absolute inset-0 bg-black opacity-50"></div> {/* Overlay for contrast */}
        <div className="container mx-auto px-6 text-center relative z-10"> {/* Ensure content is above overlay */}
          <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold mb-6 leading-tight" style={{ textShadow: '2px 2px 8px rgba(0,0,0,0.5)' }}>
            Resource Hub
          </h1>
          <p className="text-lg md:text-xl max-w-3xl mx-auto text-neutral-100 leading-relaxed" style={{ textShadow: '1px 1px 3px rgba(0,0,0,0.4)' }}> {/* Adjusted shadow for better visibility */}
            Access vital educational materials, critical support resources, and important contacts to aid in addressing drug abuse and sex trafficking.
          </p>
        </div>
      </section>

      {/* Emergency Contacts - Enhanced */}
      <section className="py-16 md:py-20 bg-red-50">
        <div className="container mx-auto px-6">
          <div className="text-center mb-12 md:mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-red-700 title-underline-white mb-3">Emergency Contacts</h2> {/* Removed text-gradient-animated, adjusted color for context */}
            <p className="text-lg text-red-700 mt-2 max-w-3xl mx-auto font-medium">
              If you or someone you know needs immediate assistance, please reach out to these critical resources without delay.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <div className="p-6 rounded-xl shadow-xl bg-white border-l-4 border-red-500 hover:shadow-2xl transition-shadow duration-300">
              <div className="flex items-center mb-4">
                <Phone size={28} className="mr-3 text-red-600 flex-shrink-0" />
                <h3 className="text-xl font-semibold text-neutral-800">National Human Trafficking Hotline</h3>
              </div>
              <p className="text-2xl font-bold mb-2 text-red-600">**************</p>
              <p className="text-neutral-600 text-sm">24/7 confidential support for victims and survivors of human trafficking. Text "HELP" or "INFO" to BeFree (233733).</p>
            </div>

            <div className="p-6 rounded-xl shadow-xl bg-white border-l-4 border-red-500 hover:shadow-2xl transition-shadow duration-300">
              <div className="flex items-center mb-4">
                <LifeBuoy size={28} className="mr-3 text-red-600 flex-shrink-0" />
                <h3 className="text-xl font-semibold text-neutral-800">988 Suicide & Crisis Lifeline</h3>
              </div>
              <p className="text-2xl font-bold mb-2 text-red-600">Call or Text 988</p>
              <p className="text-neutral-600 text-sm">24/7 free and confidential support for people in distress, prevention and crisis resources for you or your loved ones.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Downloadable Resources - Enhanced */}
      <section className="py-16 md:py-20 bg-slate-50">
        <div className="container mx-auto px-6">
          <div className="text-center mb-12 md:mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 title-underline-white mb-3">Downloadable Resources</h2> {/* Removed text-gradient-animated */}
            <p className="text-lg text-neutral-600 mt-2 max-w-3xl mx-auto">
              Access our comprehensive library of educational materials, guides, and support toolkits.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 md:gap-10">
            {/* Category 1: Educational Materials */}
            <div className="bg-white rounded-xl shadow-xl p-6 md:p-8 border border-gray-200/80">
              <div className="flex items-center mb-6">
                <BookOpen size={32} className="text-primary-600 flex-shrink-0" />
                <h3 className="text-2xl font-semibold text-neutral-800 ml-4">Educational Materials</h3>
              </div>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-primary-50 rounded-lg hover:bg-primary-100 transition-colors duration-200 group">
                  <FileText size={20} className="text-primary-500 mr-3 flex-shrink-0" />
                  <div className="flex-1">
                    <h4 className="font-medium text-neutral-700 group-hover:text-primary-700">Drug Prevention Guide for Parents</h4>
                    <p className="text-xs text-neutral-500">PDF • 2.3 MB</p>
                  </div>
                  <a href="/resources/drug-prevention-guide-parents.pdf" download="Drug_Prevention_Guide_for_Parents.pdf" className="ml-4 p-2.5 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors duration-200">
                    <Download size={18} />
                  </a>
                </div>
                <div className="flex items-center justify-between p-4 bg-primary-50 rounded-lg hover:bg-primary-100 transition-colors duration-200 group">
                  <FileText size={20} className="text-primary-500 mr-3 flex-shrink-0" />
                  <div className="flex-1">
                    <h4 className="font-medium text-neutral-700 group-hover:text-primary-700">Understanding Sex Trafficking Brochure</h4>
                    <p className="text-xs text-neutral-500">PDF • 1.5 MB</p>
                  </div>
                  <a href="/resources/understanding-sex-trafficking-brochure.pdf" download="Understanding_Sex_Trafficking_Brochure.pdf" className="ml-4 p-2.5 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors duration-200">
                    <Download size={18} />
                  </a>
                </div>
                 <div className="flex items-center justify-between p-4 bg-primary-50 rounded-lg hover:bg-primary-100 transition-colors duration-200 group">
                  <FileText size={20} className="text-primary-500 mr-3 flex-shrink-0" />
                  <div className="flex-1">
                    <h4 className="font-medium text-neutral-700 group-hover:text-primary-700">Faith Community Action Toolkit</h4>
                    <p className="text-xs text-neutral-500">PDF • 4.1 MB</p>
                  </div>
                  <a href="/resources/faith-community-action-toolkit.pdf" download="Faith_Community_Action_Toolkit.pdf" className="ml-4 p-2.5 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors duration-200">
                    <Download size={18} />
                  </a>
                </div>
              </div>
            </div>

            {/* Category 2: Support Guides */}
            <div className="bg-white rounded-xl shadow-xl p-6 md:p-8 border border-gray-200/80">
              <div className="flex items-center mb-6">
                <Users size={32} className="text-green-600 flex-shrink-0" />
                <h3 className="text-2xl font-semibold text-neutral-800 ml-4">Support & Intervention Guides</h3>
              </div>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors duration-200 group">
                  <FileText size={20} className="text-green-500 mr-3 flex-shrink-0" />
                  <div className="flex-1">
                    <h4 className="font-medium text-neutral-700 group-hover:text-green-700">Guide for Survivors of Trafficking</h4>
                    <p className="text-xs text-neutral-500">PDF • 3.1 MB</p>
                  </div>
                  <a href="/resources/guide-survivors-trafficking.pdf" download="Guide_for_Survivors_of_Trafficking.pdf" className="ml-4 p-2.5 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors duration-200">
                    <Download size={18} />
                  </a>
                </div>
                 <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors duration-200 group">
                   <FileText size={20} className="text-green-500 mr-3 flex-shrink-0" />
                  <div className="flex-1">
                    <h4 className="font-medium text-neutral-700 group-hover:text-green-700">Mental Health in Addiction Recovery</h4>
                    <p className="text-xs text-neutral-500">PDF • 2.8 MB</p>
                  </div>
                  <a href="/resources/mental-health-addiction-recovery.pdf" download="Mental_Health_in_Addiction_Recovery.pdf" className="ml-4 p-2.5 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors duration-200">
                    <Download size={18} />
                  </a>
                </div>
                <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors duration-200 group">
                   <FileText size={20} className="text-green-500 mr-3 flex-shrink-0" />
                  <div className="flex-1">
                    <h4 className="font-medium text-neutral-700 group-hover:text-green-700">Creating Safe Spaces: A Guide for Leaders</h4>
                    <p className="text-xs text-neutral-500">PDF • 3.5 MB</p>
                  </div>
                  <a href="/resources/creating-safe-spaces-guide.pdf" download="Creating_Safe_Spaces_Guide_for_Leaders.pdf" className="ml-4 p-2.5 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors duration-200">
                    <Download size={18} />
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* External Links - Enhanced */}
      <section className="py-16 md:py-20 bg-white">
        <div className="container mx-auto px-6">
          <div className="text-center mb-12 md:mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 title-underline-white mb-3">Life-Saving Resources & Support Links</h2> {/* Removed text-gradient-animated */}
            <p className="text-lg text-neutral-600 mt-2 max-w-3xl mx-auto">
              Critical resources and support organizations that could save a life. These trusted links provide immediate help, information, and ongoing support for those in need.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-slate-50 rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow duration-300 border border-slate-200">
              <h3 className="text-xl font-semibold text-neutral-800 mb-2">National Center for Missing & Exploited Children</h3>
              <p className="text-neutral-600 mb-4 text-sm">Crucial resources for child protection, safety education, and recovery assistance.</p>
              <a
                href="https://www.missingkids.org"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center text-primary-600 hover:text-primary-700 font-medium group"
              >
                Visit Website
                <ExternalLink size={16} className="ml-1.5 transition-transform duration-200 group-hover:translate-x-0.5" />
              </a>
            </div>

            <div className="bg-slate-50 rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow duration-300 border border-slate-200">
              <h3 className="text-xl font-semibold text-neutral-800 mb-2">SAMHSA National Helpline</h3>
              <p className="text-neutral-600 mb-4 text-sm">Confidential treatment referral and information service for individuals and families facing mental and/or substance use disorders.</p>
              <a
                href="https://www.samhsa.gov/find-help/national-helpline"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center text-primary-600 hover:text-primary-700 font-medium group"
              >
                Visit Website
                <ExternalLink size={16} className="ml-1.5 transition-transform duration-200 group-hover:translate-x-0.5" />
              </a>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default ResourcesPage;
