import React, { useState } from 'react';

interface TabContentProps {
  title: string;
  content: React.ReactNode;
}

interface SubSectionCardProps {
  title: string;
  description: string;
  bgColorClass: string;
}

const SubSectionCard: React.FC<SubSectionCardProps> = ({ title, description, bgColorClass }) => (
  <div className={`p-6 rounded-lg ${bgColorClass} shadow-sm`}>
    <h4 className="font-semibold text-lg mb-2 text-gray-700">{title}</h4>
    <p className="text-gray-600 text-sm">{description}</p>
  </div>
);

const EnhancedTabsSection: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('Our Mission');

  const tabs: TabContentProps[] = [
    {
      title: 'Our Mission',
      content: (
        <div>
          <h3 className="text-2xl font-semibold text-gray-800 mb-4">Transforming Communities</h3>
          <p className="text-gray-600 mb-6">
            Our mission is to create safer, healthier communities by combating drug abuse and sex trafficking through comprehensive prevention programs, education, and support services. We believe in the power of community-driven solutions and faith-based approaches to healing and recovery.
          </p>
          <div className="grid md:grid-cols-2 gap-6">
            <SubSectionCard 
              title="Prevention First" 
              description="Proactive education and awareness programs to prevent substance abuse and trafficking."
              bgColorClass="bg-blue-50 hover:bg-blue-100"
            />
            <SubSectionCard 
              title="Community Support" 
              description="Building strong support networks for individuals and families affected by these issues."
              bgColorClass="bg-green-50 hover:bg-green-100"
            />
          </div>
        </div>
      ),
    },
    {
      title: 'Our Vision',
      content: (
        <div className="space-y-5">
          <h3 className="text-2xl font-semibold text-indigo-700 mb-3">A World Free from Exploitation</h3>
          <p className="text-gray-700 leading-relaxed">
            We envision a world where every person is free from the chains of addiction and exploitation. Through our work, we strive to create communities where individuals can thrive, families can heal, and hope can flourish.
          </p>
          <blockquote className="border-l-4 border-indigo-500 pl-4 py-3 my-5 bg-indigo-50 text-indigo-800 italic rounded-r-md shadow-sm">
            "Together, we can break the cycles of abuse and build a future where every life is valued and protected."
          </blockquote>
        </div>
      ),
    },
    {
      title: 'Our Impact',
      content: (
        <div className="space-y-5">
          <h3 className="text-2xl font-semibold text-indigo-700 mb-3">The Stark Reality & Our Commitment</h3>
          <p className="text-gray-700 leading-relaxed">
            Millions are affected by substance abuse annually, with overdose deaths reaching alarming highs. Simultaneously, human trafficking ensnares countless individuals, a modern form of slavery hidden in plain sight. These crises devastate lives, families, and communities.
          </p>
          <h4 className="text-xl font-semibold text-indigo-600 mb-2 mt-3">Ministers Against DAST: Our Commitment to Change</h4>
          <p className="text-gray-700 leading-relaxed">
            Ministers against DAST is actively working to combat these issues by:
          </p>
          <ul className="list-disc list-inside text-gray-700 text-sm space-y-1.5 mb-4 pl-1 marker:text-indigo-600">
            <li>Raising Awareness: Educating communities and faith leaders to recognize and respond to these threats.</li>
            <li>Providing Support: Offering resources and direct aid to victims and those at risk.</li>
            <li>Advocating for Policy: Working towards systemic changes that protect the vulnerable and hold perpetrators accountable.</li>
          </ul>
          <p className="text-gray-700 leading-relaxed">
            We are dedicated to stemming this tide through faith, action, and unwavering commitment. (Further details and specific statistics about our impact will be added here.)
          </p>
        </div>
      ),
    },
  ];

  const activeTabData = tabs.find(tab => tab.title === activeTab);

  return (
    <section className="py-16 bg-gray-50"> {/* Changed background to gray-50 for subtle contrast */}
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-[2.5rem] font-bold text-gray-800 title-underline-white mb-3">About Our Organization</h2> {/* Added underline class, removed text-gradient-animated */}
          <p className="text-lg text-gray-600 mt-2 max-w-3xl mx-auto">
            Learn more about our mission, vision, and the impact we're making in communities worldwide
          </p>
        </div>

        {/* Enhanced Tab Navigation */}
        <div className="mb-10 flex justify-center">
          <div className="bg-white p-1.5 rounded-xl shadow-md flex flex-wrap justify-center space-x-1"> {/* Added flex-wrap and justify-center */}
            {tabs.map((tab) => (
              <button
                key={tab.title}
                onClick={() => setActiveTab(tab.title)}
                className={`px-5 py-2.5 rounded-lg text-sm font-semibold transition-all duration-200 ease-in-out
                  ${activeTab === tab.title 
                    ? 'bg-indigo-600 text-white shadow-lg transform scale-105' 
                    : 'text-gray-700 hover:bg-indigo-100 hover:text-indigo-700'
                  }`}
              >
                {tab.title}
              </button>
            ))}
          </div>
        </div>

        {/* Enhanced Tab Content Area */}
        <div className="bg-white p-8 md:p-10 rounded-xl shadow-xl border border-gray-200/70 min-h-[300px]">
          {activeTabData && activeTabData.content}
        </div>
      </div>
    </section>
  );
};

export default EnhancedTabsSection;
