<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="32" height="32">
  <defs>
    <!-- Gradient for the globe -->
    <radialGradient id="globeGradient" cx="0.3" cy="0.3" r="0.8">
      <stop offset="0%" style="stop-color:#4F46E5;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#2563EB;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </radialGradient>

    <!-- Gradient for continents -->
    <linearGradient id="continentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10B981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>

    <!-- Animated gradient for continents -->
    <linearGradient id="continentGradientAnimated" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10B981;stop-opacity:1">
        <animate attributeName="stop-color" values="#10B981;#059669;#10B981" dur="3s" repeatCount="indefinite"/>
      </stop>
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1">
        <animate attributeName="stop-color" values="#059669;#10B981;#059669" dur="3s" repeatCount="indefinite"/>
      </stop>
    </linearGradient>

    <!-- Shadow filter -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="1" dy="1" stdDeviation="1" flood-color="#000000" flood-opacity="0.3"/>
    </filter>

    <!-- Glow filter -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background circle (globe) -->
  <circle cx="16" cy="16" r="14" fill="url(#globeGradient)" filter="url(#shadow)">
    <animateTransform
      attributeName="transform"
      attributeType="XML"
      type="rotate"
      from="0 16 16"
      to="360 16 16"
      dur="4s"
      repeatCount="indefinite"/>
  </circle>
  
  <!-- Continents/landmasses -->
  <g fill="url(#continentGradientAnimated)" opacity="0.8" filter="url(#glow)">
    <!-- North America -->
    <path d="M8 10 Q12 8 15 10 Q18 12 16 15 Q14 18 10 16 Q6 14 8 10 Z">
      <animateTransform
        attributeName="transform"
        attributeType="XML"
        type="rotate"
        from="0 16 16"
        to="360 16 16"
        dur="6s"
        repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite"/>
    </path>

    <!-- Europe/Africa -->
    <path d="M18 8 Q22 6 25 9 Q28 12 26 16 Q24 20 20 18 Q16 16 18 12 Q18 10 18 8 Z">
      <animateTransform
        attributeName="transform"
        attributeType="XML"
        type="rotate"
        from="0 16 16"
        to="360 16 16"
        dur="6s"
        repeatCount="indefinite"/>
      <animate attributeName="opacity" values="1;0.6;1" dur="2s" begin="0.5s" repeatCount="indefinite"/>
    </path>

    <!-- Asia -->
    <path d="M20 20 Q24 18 28 21 Q30 24 28 27 Q25 29 22 27 Q19 25 20 22 Q20 21 20 20 Z">
      <animateTransform
        attributeName="transform"
        attributeType="XML"
        type="rotate"
        from="0 16 16"
        to="360 16 16"
        dur="6s"
        repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.8;1;0.8" dur="2s" begin="1s" repeatCount="indefinite"/>
    </path>

    <!-- South America -->
    <path d="M10 20 Q13 18 15 22 Q16 26 13 28 Q10 29 8 26 Q6 23 8 21 Q9 20 10 20 Z">
      <animateTransform
        attributeName="transform"
        attributeType="XML"
        type="rotate"
        from="0 16 16"
        to="360 16 16"
        dur="6s"
        repeatCount="indefinite"/>
      <animate attributeName="opacity" values="1;0.7;1" dur="2s" begin="1.5s" repeatCount="indefinite"/>
    </path>
  </g>
  
  <!-- Highlight/shine effect -->
  <ellipse cx="12" cy="12" rx="4" ry="6" fill="rgba(255,255,255,0.2)" opacity="0.6">
    <animateTransform
      attributeName="transform"
      attributeType="XML"
      type="rotate"
      from="0 16 16"
      to="360 16 16"
      dur="4s"
      repeatCount="indefinite"/>
  </ellipse>
  
  <!-- Orbital rings (representing global connection) -->
  <g opacity="0.6">
    <circle cx="16" cy="16" r="15" fill="none" stroke="rgba(255,255,255,0.4)" stroke-width="0.5" stroke-dasharray="2,2">
      <animateTransform
        attributeName="transform"
        attributeType="XML"
        type="rotate"
        from="0 16 16"
        to="-360 16 16"
        dur="8s"
        repeatCount="indefinite"/>
      <animate attributeName="stroke-opacity" values="0.2;0.6;0.2" dur="3s" repeatCount="indefinite"/>
    </circle>

    <circle cx="16" cy="16" r="13" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="0.3" stroke-dasharray="1,3">
      <animateTransform
        attributeName="transform"
        attributeType="XML"
        type="rotate"
        from="0 16 16"
        to="360 16 16"
        dur="12s"
        repeatCount="indefinite"/>
    </circle>
  </g>
</svg>
